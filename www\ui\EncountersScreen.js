// ui/EncountersScreen.js
// Encounters screen component

import { Component } from './Component.js';
import { loadEncounters } from '../storage/encountersStorage.js';
import { logger } from '../utils/logger.js';
import { registerBackButtonHandler } from '../capacitor/app.js';
import { getBuddyPokemon, loadTeam } from '../storage/teamStorage.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';

export class EncountersScreen extends Component {
  constructor(container, options = {}) {
    super(container, options);
    this.encounters = options.encounters || [];
    this.isLoading = true;
    this.elements = {};

    // Show loading state initially
    this.renderLoadingState();

    // Load encounters asynchronously if not provided in options
    if (!options.encounters) {
      this.loadEncountersData();
    } else {
      this.isLoading = false;
    }
  }

  /**
   * Load encounters data asynchronously
   */
  async loadEncountersData() {
    try {
      this.encounters = await loadEncounters();
      this.isLoading = false;
      this.render();
      this.addEventListeners();
    } catch (e) {
      logger.error('Error loading encounters data:', e);
      this.isLoading = false;
      this.renderErrorState(e);
    }
  }

  /**
   * Render loading state
   */
  renderLoadingState() {
    this.container.innerHTML = `
      <div class="screen-header encounters-header">
        <button class="back-btn" id="encounters-back-btn" aria-label="Zurück">
          <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
        </button>
        <h1>Begegnungen</h1>
        <div class="header-right"></div>
      </div>
      <div class="encounters-loading">
        <p>Loading encounters data...</p>
      </div>
    `;

    this.elements.backButton = this.container.querySelector('#encounters-back-btn');
    this.addEventListeners();
  }

  /**
   * Render error state
   * @param {Error} error - The error that occurred
   */
  renderErrorState(error) {
    logger.error('Error rendering Encounters screen:', error);
    this.container.innerHTML = `
      <div class="screen-header encounters-header">
        <button class="back-btn" id="encounters-back-btn" aria-label="Zurück">
          <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
        </button>
        <h1>Begegnungen</h1>
        <div class="header-right"></div>
      </div>
      <div class="encounters-error">
        <p>Error loading encounters data. Please try again.</p>
      </div>
    `;

    this.elements.backButton = this.container.querySelector('#encounters-back-btn');
    this.isRendered = true;
  }

  /**
   * Render the encounters screen
   * @returns {HTMLElement} - The rendered container
   */
  render() {
    if (this.isLoading) {
      return this.renderLoadingState();
    }

    try {
      // Sort encounters by timestamp (newest first)
      const encounters = [...this.encounters].sort((a, b) => b.timestamp - a.timestamp);

      // Render the header and list
      this.container.innerHTML = `
        <div class="screen-header encounters-header">
          <button class="back-btn" id="encounters-back-btn" aria-label="Zurück">
            <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
          </button>
          <h1>Begegnungen</h1>
          <div class="header-right"></div>
        </div>
        <ul class="encounters-list">
          ${encounters.map(encounter => this.renderEncounterItem(encounter)).join('')}
        </ul>
      `;

      // Store elements for event handling
      this.elements.backButton = this.container.querySelector('#encounters-back-btn');

      this.isRendered = true;
      return this.container;
    } catch (e) {
      return this.renderErrorState(e);
    }
  }

  /**
   * Render an encounter item
   * @param {Object} encounter - The encounter data
   * @returns {string} - The HTML for the item
   */
  renderEncounterItem(encounter) {
    const date = new Date(encounter.timestamp);
    const formattedDate = date.toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Bestimme den Level-Text: "Lvl. X" wenn Level vorhanden, sonst "Lvl. ?"
    const levelText = encounter.level !== undefined ? `Lvl. ${encounter.level}` : 'Lvl. ?';

    // Get German name for display
    const displayName = getGermanPokemonName(encounter);

    return `
      <li class="encounter-item" data-id="${encounter.id}">
        <div class="pokemon-info">
          <span class="pokemon-name">${displayName}</span>
          <span class="pokemon-level">${levelText}</span>
        </div>
        <div class="encounter-actions">
          <button class="battle-btn" data-id="${encounter.id}" aria-label="Kampf starten">
            <img src="./icons/materialicons/swords.svg" alt="Kampf" class="icon-svg" width="24" height="24" />
          </button>
        </div>
        <span class="encounter-date">${formattedDate}</span>
      </li>
    `;
  }

  /**
   * Add event listeners
   */
  addEventListeners() {
    if (this.elements.backButton) {
      this.addEventListener(this.elements.backButton, 'click', () => {
        // Close overlay
        const overlay = this.container.closest('#encounters-overlay');
        if (overlay) {
          overlay.style.display = 'none';
          overlay.dispatchEvent(new Event('closeEncounters'));
        }
      });
    }

    // Add event listeners to battle buttons
    const battleButtons = this.container.querySelectorAll('.battle-btn');
    battleButtons.forEach(button => {
      this.addEventListener(button, 'click', (e) => {
        e.stopPropagation();
        const pokemonId = button.getAttribute('data-id');
        this.startBattle(pokemonId);
      });
    });
  }

  /**
   * Start a battle with the selected Pokemon
   * @param {string} pokemonId - The ID of the Pokemon to battle
   */
  async startBattle(pokemonId) {
    try {
      // Find the encounter in the encounters list
      const encounter = this.encounters.find(e => e.id === pokemonId);
      if (!encounter) {
        logger.error(`Encounter with ID ${pokemonId} not found`);
        return;
      }

      logger.debug(`Starting battle with ${encounter.name} (ID: ${pokemonId})`);

      // Get Pokemon details from pokedex data
      const pokedexResp = await fetch('./pokedex-151.json');
      const pokedexData = await pokedexResp.json();

      // Suche nach dem Pokémon anhand des Namens statt der ID
      const pokemonData = pokedexData.find(p =>
        p.name === encounter.name ||
        (p.de && p.de === encounter.name)
      );

      if (!pokemonData) {
        logger.error(`Pokemon with name ${encounter.name} not found in pokedex data`);
        return;
      }

      // Create wild Pokemon object for battle
      const wildPokemon = {
        id: encounter.id,
        name: encounter.name,
        level: encounter.level || 5,
        types: pokemonData.types || ['Normal'],
        image: pokemonData.image_url || `./src/PokemonSprites/${pokemonData.dex_number}.png`,
        // Zusätzliche Daten aus dem Pokedex für die Anzeige im Battle-Screen
        dex_number: pokemonData.dex_number,
        pokedexId: pokemonData.id, // Die ID aus dem Pokedex für spätere Referenzen
        rarity: pokemonData.rarity || 'common' // Wichtig für die XP-Berechnung
      };

      // Debug-Ausgabe für das wilde Pokémon
      logger.debug(`Wild Pokemon for battle: ${wildPokemon.name} (Lvl ${wildPokemon.level}, Rarity: ${wildPokemon.rarity})`);
      logger.debug(`Wild Pokemon details: ID=${wildPokemon.id}, Types=${JSON.stringify(wildPokemon.types)}, Pokedex ID=${wildPokemon.pokedexId}`);


      // Get buddy Pokemon (first Pokemon in team) for battle
      const buddyPokemon = await getBuddyPokemon();

      // Import Pokemon class to handle experience
      const { default: Pokemon } = await import('../Pokemon.js');

      let playerPokemon;

      if (buddyPokemon) {
        // Use buddy Pokemon from team
        logger.debug(`Using buddy Pokemon for battle: ${buddyPokemon.name} (Lvl ${buddyPokemon.level})`);

        // Create player Pokemon object for battle using the Pokemon class
        const types = buddyPokemon.types || ['Normal'];
        const mainType = types[0].toLowerCase();

        // Create evolution data object with required properties
        const evolutionData = {
          types: types,
          rarity: buddyPokemon.rarity || 'common',
          image_url: buddyPokemon.image || `./src/PokemonSprites/${buddyPokemon.dex_number || '0'}.png`
        };

        // Create Pokemon instance
        playerPokemon = new Pokemon(
          buddyPokemon.name,
          mainType,
          buddyPokemon.level || 5,
          evolutionData
        );

        // Set additional properties
        playerPokemon.id = buddyPokemon.id;
        playerPokemon.image = buddyPokemon.image || evolutionData.image_url;
        playerPokemon.experience = buddyPokemon.experience || 0;
        playerPokemon.types = types;

        // Debug output for player Pokemon
        logger.debug(`Buddy Pokemon for battle: ${playerPokemon.name} (Lvl ${playerPokemon.level}, Rarity: ${playerPokemon.rarity})`);
        logger.debug(`Buddy Pokemon details: ID=${playerPokemon.id}, Types=${JSON.stringify(playerPokemon.types)}`);
        logger.debug(`Buddy Pokemon experience: ${playerPokemon.experience} XP`);
      } else {
        // Fallback to starter Pokemon if no buddy is available
        logger.debug('No buddy Pokemon found, using starter Pikachu as fallback');

        // Get starter Pokemon data for player
        const starterResp = await fetch('./starter-pokemon.json');
        let starterPokemon = await starterResp.json();

        // Load current data from localStorage if available
        const storedStarterPokemon = localStorage.getItem('starterPokemon');
        if (storedStarterPokemon) {
          try {
            const parsedData = JSON.parse(storedStarterPokemon);
            // Merge the stored data with the loaded data
            starterPokemon = { ...starterPokemon, ...parsedData };
            logger.debug(`Loaded starter Pokemon from localStorage: Level ${starterPokemon.level}, XP: ${starterPokemon.experience}`);

            // Import the experience system to calculate the correct level
            const { getExpCurveForRarity, getLevelForExp } = await import('../services/experience-system.js');

            // Calculate the correct level based on experience points
            if (typeof starterPokemon.experience === 'number' && starterPokemon.experience > 0) {
              const curve = getExpCurveForRarity(starterPokemon.rarity || 'starter');
              const calculatedLevel = getLevelForExp(starterPokemon.experience, curve);

              // Update the level if it doesn't match the calculated level
              if (calculatedLevel !== starterPokemon.level) {
                logger.debug(`Updating starter Pokemon level from ${starterPokemon.level} to ${calculatedLevel} based on XP (${starterPokemon.experience})`);
                starterPokemon.level = calculatedLevel;

                // Save the updated level to localStorage
                localStorage.setItem('starterPokemon', JSON.stringify(starterPokemon));
              }
            }
          } catch (e) {
            logger.error('Error parsing stored starter Pokemon:', e);
          }
        }

        // Create evolution data object with required properties
        const evolutionData = {
          types: ['electric'],
          rarity: starterPokemon.rarity || 'starter',
          image_url: './src/PokemonSprites/25.png'
        };

        // Create player Pokemon object for battle using the Pokemon class
        playerPokemon = new Pokemon('Pikachu', 'electric', starterPokemon.level || 5, evolutionData);
        playerPokemon.id = 'starter-pikachu'; // Unique ID for starter Pikachu
        playerPokemon.image = './src/PokemonSprites/25.png';

        // Set experience points if available
        if (typeof starterPokemon.experience === 'number') {
          playerPokemon.experience = starterPokemon.experience;
        }

        // Debug output for player Pokemon
        logger.debug(`Starter Pokemon for battle: ${playerPokemon.name} (Lvl ${playerPokemon.level}, Rarity: ${playerPokemon.rarity})`);
        logger.debug(`Starter Pokemon details: ID=${playerPokemon.id}, Types=${JSON.stringify(playerPokemon.types)}`);
        logger.debug(`Starter Pokemon experience: ${playerPokemon.experience || 0} XP`);
      }

      // Close the encounters overlay
      const overlay = this.container.closest('#encounters-overlay');
      if (overlay) {
        overlay.style.display = 'none';
      }

      // Import the necessary modules
      const { openBattleScreenWithCallback } = await import('./BattleScreen.js');
      const { removeEncounter } = await import('../storage/encountersStorage.js');
      const { addCaughtPokemon } = await import('../storage/caughtPokemonStorage.js');
      const { pokemonManager } = await import('../services/pokemon-manager.js');

      // Open the battle screen with the selected Pokemon
      openBattleScreenWithCallback(playerPokemon, wildPokemon, async (result) => {
        if (result.playerWins) {
          // Player won, add Pokemon to caught list and central storage
          logger.debug(`Player won battle against ${wildPokemon.name}, adding to caught list`);

          // Initialize the Pokemon manager
          await pokemonManager.initialize();

          // Add to central Pokemon manager first
          await pokemonManager.addPokemon(wildPokemon);
          logger.debug(`Added Pokemon ${wildPokemon.name} to central storage after winning battle`);

          // Also add to caught Pokemon list for backward compatibility
          await addCaughtPokemon(wildPokemon);

          // XP is already added in BattleScreen.js's animateExperienceGain method
          // We just need to update the UI and show level up alerts if needed
          if (result.experienceGained) {
            // Debug-Ausgabe für die Erfahrungspunkte
            logger.debug(`XP Update - Player Pokemon ${playerPokemon.name} (Lvl ${playerPokemon.level}) gained ${result.experienceGained} XP in battle`);
            logger.debug(`XP Update - Current XP: ${playerPokemon.experience || 0}`);

            // Check if we're using a buddy Pokemon or the starter
            if (buddyPokemon) {
              // Update buddy Pokemon in team
              try {
                // Get current team
                const team = await loadTeam();

                // Find the buddy in the team (should be the first Pokemon)
                if (team.length > 0 && team[0].id === playerPokemon.id) {
                  // Get the latest Pokemon data from the manager
                  await pokemonManager.initialize();
                  const updatedPokemon = pokemonManager.getPokemonById(playerPokemon.id);

                  if (updatedPokemon) {
                    logger.debug(`XP Update - Retrieved updated Pokemon from manager: ${updatedPokemon.name} (Lvl ${updatedPokemon.level}, XP: ${updatedPokemon.experience})`);

                    // Check if the Pokemon leveled up by comparing levels
                    const didLevelUp = updatedPokemon.level > playerPokemon.level;

                    if (didLevelUp) {
                      logger.debug(`${playerPokemon.name} leveled up from ${playerPokemon.level} to ${updatedPokemon.level}!`);

                      // Show level up alert
                      setTimeout(() => {
                        alert(`Glückwunsch! Dein ${updatedPokemon.name} ist auf Level ${updatedPokemon.level} aufgestiegen!`);
                      }, 1000);
                    }
                  } else {
                    logger.warn(`Could not find updated Pokemon with ID ${playerPokemon.id} in manager`);
                  }
                } else {
                  logger.warn(`Buddy Pokemon not found in team or not at first position`);
                }
              } catch (e) {
                logger.error('Error checking buddy Pokemon level:', e);
              }
            } else {
              // For starter Pokemon, check localStorage
              try {
                const starterPokemon = JSON.parse(localStorage.getItem('starterPokemon') || '{}');

                // Get the latest Pokemon data from the manager
                await pokemonManager.initialize();
                const updatedPokemon = pokemonManager.getPokemonById(playerPokemon.id);

                if (updatedPokemon) {
                  // Update localStorage with the latest data
                  starterPokemon.experience = updatedPokemon.experience;
                  starterPokemon.level = updatedPokemon.level;
                  starterPokemon.rarity = updatedPokemon.rarity || 'starter';

                  localStorage.setItem('starterPokemon', JSON.stringify(starterPokemon));
                  logger.debug(`XP Update - Starter Pokemon saved to localStorage: ${JSON.stringify(starterPokemon)}`);

                  // Check if the Pokemon leveled up
                  const didLevelUp = updatedPokemon.level > playerPokemon.level;

                  if (didLevelUp) {
                    logger.debug(`${playerPokemon.name} leveled up from ${playerPokemon.level} to ${updatedPokemon.level}!`);

                    // Show level up alert with German name
                    setTimeout(() => {
                      const displayName = getGermanPokemonName(updatedPokemon);
                      alert(`Glückwunsch! Dein ${displayName} ist auf Level ${updatedPokemon.level} aufgestiegen!`);
                    }, 1000);
                  }
                } else {
                  logger.warn(`Could not find updated Pokemon with ID ${playerPokemon.id} in manager`);
                }
              } catch (e) {
                logger.error('Error updating starter Pokemon in localStorage:', e);
              }
            }
          }
        } else {
          // Player lost, Pokemon escapes
          logger.debug(`Player lost battle against ${wildPokemon.name}, Pokemon escapes`);
        }

        // Remove the Pokemon from encounters list regardless of outcome
        await removeEncounter(pokemonId);

        // Refresh the encounters list
        this.loadEncountersData();
      });
    } catch (e) {
      logger.error('Error starting battle:', e);
    }
  }
}

/**
 * Open the encounters screen
 */
export function openEncountersScreen() {
  // Import fabManager dynamically to avoid circular dependencies
  import('../ui/FabManager.js').then(({ fabManager }) => {
    // Hide all FAB buttons to prevent errors
    fabManager.hideAllButtons();
  }).catch(e => {
    logger.warn('Could not import fabManager:', e);
  });

  let overlay = document.getElementById('encounters-overlay');

  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'encounters-overlay';
    overlay.className = 'encounters-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100vw';
    overlay.style.height = '100vh';
    overlay.style.background = 'var(--standard-background-color, #f4f4f4)';
    overlay.style.zIndex = '10020';
    overlay.style.overflowY = 'auto';
    document.body.appendChild(overlay);
  }

  overlay.style.display = 'block';

  // Create the encounters screen - it will handle loading data asynchronously
  new EncountersScreen(overlay);

  // Funktion zum Schließen des Overlays
  const closeOverlay = () => {
    overlay.style.display = 'none';
    overlay.dispatchEvent(new Event('closeEncounters'));

    // Show all FAB buttons when overlay is closed
    import('../ui/FabManager.js').then(({ fabManager }) => {
      fabManager.showAllButtons();
    }).catch(e => {
      logger.warn('Could not import fabManager:', e);
    });
  };

  // Zurück-Button des Smartphones abfangen
  const removeBackButtonHandler = registerBackButtonHandler(closeOverlay);

  // Close overlay on back event
  overlay.addEventListener('closeEncounters', () => {
    // Event-Listener für den Zurück-Button entfernen
    removeBackButtonHandler();
    overlay.style.display = 'none';

    // Show all FAB buttons when overlay is closed
    import('../ui/FabManager.js').then(({ fabManager }) => {
      fabManager.showAllButtons();
    }).catch(e => {
      logger.warn('Could not import fabManager:', e);
    });
  });
}
