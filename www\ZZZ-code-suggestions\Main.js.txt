// Die challengeTrainer Funktion in main.js ersetzen:
window.challengeTrainer = async function(trainerId) {
    const trainer = gameState.trainers.find(t => t.id === trainerId);
    if (!trainer) {
        alert('Trainer nicht gefunden!');
        return;
    }
    
    // Prüfe Entfernung
    if (!gameState.lastUserLatLng) {
        alert('Position nicht verfügbar!');
        return;
    }
    
    const distance = distanceMeters(
        gameState.lastUserLatLng.lat,
        gameState.lastUserLatLng.lng,
        trainer.lat,
        trainer.lng
    );
    
    if (distance > 50) {
        alert(`Zu weit entfernt! Komm näher (${Math.round(distance)}m entfernt, max. 50m)`);
        return;
    }
    
    // Prüfe ob Spieler genau 6 Pokemon im Team hat
    try {
        await pokemonManager.initialize();
        const playerTeam = pokemonManager.getTeamPokemon();
        
        if (!playerTeam || playerTeam.length !== 6) {
            alert('Du brauchst genau 6 Pokémon in deinem Team für einen Trainerkampf! Gehe zum Team-Bildschirm und stelle dein Team zusammen.');
            return;
        }
        
        // Prüfe ob Trainer genau 6 Pokemon hat
        if (!trainer.team || trainer.team.length !== 6) {
            alert('Der Trainer hat kein vollständiges Team! Kampf kann nicht gestartet werden.');
            return;
        }
        
        // Starte Trainerkampf
        await trainerBattleScreen.startBattle(trainer);
    } catch (e) {
        logger.error('Error starting trainer battle:', e);
        alert('Fehler beim Starten des Trainerkampfs: ' + e.message);
    }
};
