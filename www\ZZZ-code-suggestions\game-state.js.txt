// In der GameState-Klasse hinzufügen:

constructor() {
    // ... bestehender Code ...
    
    // Trainer related
    this.trainers = [];
    this.trainerMarkers = new Map(); // Map of id -> { marker, popupOpen }
}

/**
 * Add a trainer to the game world
 * @param {Trainer} trainer - The trainer to add
 */
addTrainer(trainer) {
    this.trainers.push(trainer);
    this.events.emit('trainersUpdated', this.trainers);
}

/**
 * Remove a trainer from the game world
 * @param {string} id - The ID of the trainer to remove
 * @returns {Trainer|null} - The removed trainer or null if not found
 */
removeTrainer(id) {
    const index = this.trainers.findIndex(t => t.id === id);
    if (index === -1) return null;
    
    const trainer = this.trainers[index];
    this.trainers.splice(index, 1);
    this.events.emit('trainersUpdated', this.trainers);
    return trainer;
}
