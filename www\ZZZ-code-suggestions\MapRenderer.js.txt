/**
 * Render all trainers on the map
 * @param {boolean} recreateAll - Whether to recreate all markers
 */
async renderTrainers(recreateAll = false) {
    const { trainers, trainerMarkers, map } = gameState;
    
    // If recreateAll is true, remove all markers and recreate
    if (recreateAll) {
        trainerMarkers.forEach(entry => map.removeLayer(entry.marker));
        trainerMarkers.clear();
    }
    
    // Track which trainer IDs are currently being displayed
    const currentIds = new Set(trainers.map(t => t.id));
    const existingIds = new Set(trainerMarkers.keys());
    
    // Remove markers for trainers that no longer exist
    for (const id of existingIds) {
        if (!currentIds.has(id)) {
            map.removeLayer(trainerMarkers.get(id).marker);
            trainerMarkers.delete(id);
        }
    }
    
    // Check if player has required team size
    await pokemonManager.initialize();
    const playerTeam = pokemonManager.getTeamPokemon();
    const playerHasRequiredTeam = playerTeam && playerTeam.length === 6;
    
    // Add or update markers for all trainers
    trainers.forEach((trainer) => {
        let entry = trainerMarkers.get(trainer.id);
        
        if (!entry) {
            // Create new marker
            const iconUrl = trainer.variant.mapSprite;
            const icon = L.icon({
                iconUrl: iconUrl,
                iconSize: [32, 32],
                iconAnchor: [16, 16],
                popupAnchor: [0, -32]
            });
            
            const marker = L.marker([trainer.lat, trainer.lng], { icon: icon });
            
            // Erstelle Team-Info für Popup
            const teamInfo = trainer.team.map(pokemon => {
                // Finde deutschen Namen
                const pokedexEntry = gameState.pokedexData.find(p => p.dex_number === pokemon.dex_number);
                const displayName = pokedexEntry?.de || pokemon.name;
                return `${displayName} (Lv. ${pokemon.level})`;
            }).join('<br>');
            
            // Button state based on team requirement
            const buttonDisabled = !playerHasRequiredTeam ? 'disabled' : '';
            const buttonClass = !playerHasRequiredTeam ? 'challenge-button' : 'challenge-button';
            const warningText = !playerHasRequiredTeam ? '<div class="team-requirement-warning">Du brauchst 6 Pokémon im Team!</div>' : '';
            
            const popupHtml = `
                <div class="trainer-popup">
                    <b>${trainer.name}</b><br>
                    <i>${trainer.trainerType}</i><br>
                    <br>
                    <b>Team (Ø Lv. ${trainer.averageLevel}):</b><br>
                    <div class="team-list">${teamInfo}</div>
                    <br>
                    <button onclick="challengeTrainer('${trainer.id}')" class="${buttonClass}" ${buttonDisabled}>
                        Herausfordern
                    </button>
                    ${warningText}
                </div>
            `;
            
            marker.bindPopup(popupHtml);
            marker.addTo(map);
            
            marker.on('popupopen', () => {
                if (trainerMarkers.get(trainer.id)) trainerMarkers.get(trainer.id).popupOpen = true;
            });
            
            marker.on('popupclose', () => {
                if (trainerMarkers.get(trainer.id)) trainerMarkers.get(trainer.id).popupOpen = false;
            });
            
            trainerMarkers.set(trainer.id, { marker, popupOpen: false });
        } else {
            // Update existing marker position
            entry.marker.setLatLng([trainer.lat, trainer.lng]);
        }
    });
}
