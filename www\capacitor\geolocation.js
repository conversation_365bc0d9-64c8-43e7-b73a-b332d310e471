// capacitor/geolocation.js
// Helper module for Capacitor Geolocation

import { config } from '../config.js';
import { logger } from '../utils/logger.js';

// Check if we're in test mode (for browser testing without Capacitor)
const isTestMode = window.location.href.includes('test.html');

/**
 * Start watching the user's position
 * @param {Function} callback - Callback function(position, error)
 * @returns {Function} - Function to stop watching
 */
export async function startWatchPosition(callback) {
  // In test mode, use browser geolocation or mock data
  if (isTestMode) {
    logger.info('Using test mode for geolocation watching');

    // For testing, immediately return a fixed position
    const mockPosition = {
      coords: {
        latitude: 52.520008,
        longitude: 13.404954,
        accuracy: 10,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    };

    // Call the callback with the mock position
    setTimeout(() => {
      logger.debug('Test mode: sending mock position');
      callback(mockPosition);
    }, 100);

    // Return a no-op stop function
    return () => {
      logger.info('Test mode: stopping position watch (no-op)');
    };
  }

  // Normal mode - use Capacitor
  const Geolocation = window.Capacitor?.Plugins?.Geolocation;

  if (!Geolocation) {
    logger.error('Capacitor Geolocation Plugin not available');
    callback(null, { code: 0, message: 'Capacitor Geolocation Plugin not available.' });
    return () => {};
  }

  let watchId = null;

  try {
    // Get options from config
    const { enableHighAccuracy, timeout, maximumAge } = config.geolocation;

    watchId = await Geolocation.watchPosition({
      enableHighAccuracy,
      timeout,
      maximumAge
    }, (pos, err) => {
      if (err) {
        logger.error('Geolocation error:', err);
        callback(null, err);
      } else {
        logger.debug(`Geolocation update: lat=${pos.coords.latitude}, lng=${pos.coords.longitude}, accuracy=${pos.coords.accuracy}`);
        callback(pos);
      }
    });

    logger.info('Started watching position with ID:', watchId);
  } catch (e) {
    logger.error('Error starting position watch:', e);
    callback(null, e);
  }

  // Return stop function
  return () => {
    if (Geolocation && watchId != null) {
      logger.info('Stopping position watch with ID:', watchId);
      Geolocation.clearWatch({ id: watchId });
    }
  };
}

/**
 * Get the user's current position once
 * @returns {Promise<Object>} - The position object
 */
export async function getCurrentPosition() {
  // In test mode, use browser geolocation API
  if (isTestMode) {
    return new Promise((resolve, reject) => {
      // For testing, return a fixed position in Berlin
      const mockPosition = {
        coords: {
          latitude: 52.520008,
          longitude: 13.404954,
          accuracy: 10,
          altitude: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null
        },
        timestamp: Date.now()
      };

      logger.debug(`Using mock position for testing: lat=${mockPosition.coords.latitude}, lng=${mockPosition.coords.longitude}`);

      resolve(mockPosition);
    });
  }

  // Normal mode - use Capacitor
  const Geolocation = window.Capacitor?.Plugins?.Geolocation;

  if (!Geolocation) {
    logger.error('Capacitor Geolocation Plugin not available');
    throw new Error('Capacitor Geolocation Plugin not available.');
  }

  try {
    // Get options from config
    const { enableHighAccuracy, timeout, maximumAge } = config.geolocation;

    const position = await Geolocation.getCurrentPosition({
      enableHighAccuracy,
      timeout,
      maximumAge
    });

    logger.debug(`Got current position: lat=${position.coords.latitude}, lng=${position.coords.longitude}, accuracy=${position.coords.accuracy}`);

    return position;
  } catch (e) {
    logger.error('Error getting current position:', e);
    throw e;
  }
}

/**
 * Check if location permissions are granted
 * @returns {Promise<boolean>} - Whether permissions are granted
 */
export async function checkLocationPermissions() {
  // In test mode, always return true
  if (isTestMode) {
    logger.info('Test mode: location permissions check (always granted)');
    return true;
  }

  const Geolocation = window.Capacitor?.Plugins?.Geolocation;

  if (!Geolocation) {
    logger.error('Capacitor Geolocation Plugin not available');
    return false;
  }

  try {
    const permissions = await Geolocation.checkPermissions();
    const granted = permissions.location === 'granted';

    logger.info('Location permissions:', permissions.location);
    return granted;
  } catch (e) {
    logger.error('Error checking location permissions:', e);
    return false;
  }
}

/**
 * Request location permissions
 * @returns {Promise<boolean>} - Whether permissions are granted
 */
export async function requestLocationPermissions() {
  // In test mode, always return true
  if (isTestMode) {
    logger.info('Test mode: location permissions request (always granted)');
    return true;
  }

  const Geolocation = window.Capacitor?.Plugins?.Geolocation;

  if (!Geolocation) {
    logger.error('Capacitor Geolocation Plugin not available');
    return false;
  }

  try {
    const permissions = await Geolocation.requestPermissions();
    const granted = permissions.location === 'granted';

    logger.info('Location permissions after request:', permissions.location);
    return granted;
  } catch (e) {
    logger.error('Error requesting location permissions:', e);
    return false;
  }
}
